"use client"

import { useRouter } from 'next/navigation';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@/app/components/ui/dialog';
import {
    Card,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/app/components/ui/card';
import { Badge } from '@/app/components/ui/badge';
import { FileChartColumn, Grid3x3, ListTodo, MoveRight, NotepadText, Package, User } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

interface ClientNavigationModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    clientId: string | null;
    clientName?: string;
}

export function ClientNavigationModal({
    open,
    onOpenChange,
    clientId,
    clientName,
}: ClientNavigationModalProps) {
    const { data: session } = useSession();
    const [userRole, setUserRole] = useState('');
    const router = useRouter();

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setUserRole(user?.role || '');
                    }
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
            }
        };

        fetchUserRole();
    }, [session]);

    if (!clientId) return null;

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="text-lg font-semibold">Navegação</DialogTitle>
                    {clientName && (
                        <DialogDescription className="text-sm text-muted-foreground">
                            Selecione uma opção para {clientName}
                        </DialogDescription>
                    )}
                </DialogHeader>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4">
                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT') && (
                        <Card
                            className="cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-900 focus:outline-none focus:ring-2 focus:ring-primary2 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-colors dark:bg-zinc-800/50"
                            onClick={() => {
                                router.push(`/clients/${clientId}`);
                                onOpenChange(false);
                            }}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    router.push(`/clients/${clientId}`);
                                    onOpenChange(false);
                                }
                            }}
                        >

                            <CardHeader className="pb-1 sm:pb-2 p-2 sm:p-6">
                                <CardTitle className="text-sm sm:text-base flex items-center gap-1 sm:gap-2 whitespace-nowrap">
                                    <User className="text-primary2 flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="truncate">Perfil</span>
                                </CardTitle>
                                <CardDescription className="text-xs sm:text-sm">
                                    Visualize e edite o perfil do cliente e outras informações relevantes.
                                </CardDescription>
                            </CardHeader>

                            <CardFooter className="pt-0 px-2 sm:px-6 pb-2 sm:pb-6 flex justify-between items-center">
                                <Badge variant="outline" className="text-xs sm:text-sm px-1 sm:px-2 py-0 sm:py-1">
                                    etapa 1
                                </Badge>
                                <div className="p-0 h-auto">
                                    <span className="sr-only">Ir para perfil</span>
                                    <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                </div>
                            </CardFooter>
                        </Card>
                    )}

                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT' || userRole === 'DESIGNER_SENIOR' || userRole === 'COPY') && (
                        <Card
                            className="cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-primary2 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-colors dark:bg-zinc-800/50"
                            onClick={() => {
                                router.push(`/monthly-planning/${clientId}`);
                                onOpenChange(false);
                            }}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    router.push(`/monthly-planning/${clientId}`);
                                    onOpenChange(false);
                                }
                            }}
                        >
                            <CardHeader className="pb-1 sm:pb-2 p-2 sm:p-6">
                                <CardTitle className="text-sm sm:text-base flex items-center gap-1 sm:gap-2 whitespace-nowrap">
                                    <NotepadText className="text-primary2 flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="truncate">Planejamentos</span>
                                </CardTitle>
                                <CardDescription className="text-xs sm:text-sm">
                                    Visualize e gerencie os planejamentos mensais
                                </CardDescription>
                            </CardHeader>

                            <CardFooter className="pt-0 px-2 sm:px-6 pb-2 sm:pb-6 flex justify-between items-center">
                                <Badge variant="outline" className="text-xs sm:text-sm px-1 sm:px-2 py-0 sm:py-1">
                                    etapa 2
                                </Badge>
                                <div className="p-0 h-auto">
                                    <span className="sr-only">Ir para planejamentos</span>
                                    <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                </div>
                            </CardFooter>

                        </Card>
                    )}

                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT' || userRole === 'DESIGNER_SENIOR') && (
                        <Card
                            className="cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-primary2 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-colors dark:bg-zinc-800/50"
                            onClick={() => {
                                router.push(`/feed-structuring/${clientId}`);
                                onOpenChange(false);
                            }}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    router.push(`/feed-structuring/${clientId}`);
                                    onOpenChange(false);
                                }
                            }}
                        >
                            <CardHeader className="pb-1 sm:pb-2 p-2 sm:p-6">
                                <CardTitle className="text-sm sm:text-base flex items-center gap-1 sm:gap-2 whitespace-nowrap">
                                    <Grid3x3 className="text-primary2 flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="">Estrut. do Feed</span>
                                </CardTitle>
                                <CardDescription className="text-xs sm:text-sm">
                                    Organize e estruture o feed mensal
                                </CardDescription>
                            </CardHeader>

                            <CardFooter className="pt-0 px-2 sm:px-6 pb-2 sm:pb-6 flex justify-between items-center">
                                <Badge variant="outline" className="text-xs sm:text-sm px-1 sm:px-2 py-0 sm:py-1">
                                    etapa 3
                                </Badge>
                                <div className="p-0 h-auto">
                                    <span className="sr-only">Ir para estruturação</span>
                                    <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                </div>
                            </CardFooter>
                        </Card>
                    )}

                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT') && (
                        <Card
                            className="cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-primary2 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-colors dark:bg-zinc-800/50 relative overflow-hidden"
                            onClick={() => {
                                router.push(`/requirements/${clientId}`);
                                onOpenChange(false);
                            }}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    router.push(`/requirements/${clientId}`);
                                    onOpenChange(false);
                                }
                            }}
                        >
                            <CardHeader className="pb-1 sm:pb-2 p-2 sm:p-6">
                                <CardTitle className="text-sm sm:text-base flex items-center gap-1 sm:gap-2 whitespace-nowrap">
                                    <ListTodo className="text-primary2 flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="truncate">Demandas</span>
                                </CardTitle>
                                <CardDescription className="text-xs sm:text-sm">
                                    Visualize e gerencie demandas e tarefas
                                </CardDescription>
                            </CardHeader>

                            <CardFooter className="pt-0 px-2 sm:px-6 pb-2 sm:pb-6 flex justify-between items-center">
                                <Badge variant="outline" className="text-xs sm:text-sm px-1 sm:px-2 py-0 sm:py-1">
                                    etapa 4
                                </Badge>
                                <div className="p-0 h-auto">
                                    <span className="sr-only">Ir para demandas</span>
                                    <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                </div>
                            </CardFooter>
                        </Card>
                    )}

                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT' || userRole === 'DESIGNER_SENIOR') && (
                        <Card
                            className="cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-primary2 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-colors dark:bg-zinc-800/50 relative overflow-hidden "
                            onClick={() => {
                                router.push(`/deliveries/${clientId}`);
                                onOpenChange(false);
                            }}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    router.push(`/deliveries/${clientId}`);
                                    onOpenChange(false);
                                }
                            }}
                        >
                            <CardHeader className="pb-1 sm:pb-2 p-2 sm:p-6">
                                <CardTitle className="text-sm sm:text-base flex items-center gap-1 sm:gap-2 whitespace-nowrap">
                                    <Package className="text-primary2 flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="truncate">Entregas</span>
                                </CardTitle>
                                <CardDescription className="text-xs sm:text-sm">
                                    Visualize e gerencie as entregas de conteúdo
                                </CardDescription>
                            </CardHeader>

                            <CardFooter className="pt-0 px-2 sm:px-6 pb-2 sm:pb-6 flex justify-between items-center">
                                <Badge variant="outline" className="text-xs sm:text-sm px-1 sm:px-2 py-0 sm:py-1">
                                    etapa 7
                                </Badge>
                                <div className="p-0 h-auto">
                                    <span className="sr-only">Ir para demandas</span>
                                    <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                </div>
                            </CardFooter>
                        </Card>
                    )}

                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT') && (
                        <Card
                            className="cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-primary2 focus:ring-offset-2 dark:focus:ring-offset-zinc-900 transition-colors dark:bg-zinc-800/50"
                            onClick={() => {
                                router.push(`/results-report/${clientId}`);
                                onOpenChange(false);
                            }}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    router.push(`/results-report/${clientId}`);
                                    onOpenChange(false);
                                }
                            }}
                        >
                            <CardHeader className="pb-1 sm:pb-2 p-2 sm:p-6">
                                <CardTitle className="text-sm sm:text-base flex items-center gap-1 sm:gap-2 whitespace-nowrap">
                                    <FileChartColumn className="text-primary2 flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                                    <span className="truncate">Resultados</span>
                                </CardTitle>
                                <CardDescription className="text-xs sm:text-sm">
                                    Visualize relatórios e métricas de desempenho
                                </CardDescription>
                            </CardHeader>

                            <CardFooter className="pt-0 px-2 sm:px-6 pb-2 sm:pb-6 flex justify-between items-center">
                                <Badge variant="outline" className="text-xs sm:text-sm px-1 sm:px-2 py-0 sm:py-1">
                                    etapa 10
                                </Badge>
                                <div className="p-0 h-auto">
                                    <span className="sr-only">Ir para resultados</span>
                                    <MoveRight className="w-4 h-4 sm:w-5 sm:h-5" />
                                </div>
                            </CardFooter>
                        </Card>
                    )}

                    {userRole === 'DESIGNER_JUNIOR' && (
                        <p className='text-sm text-muted-foreground border-t border-zinc-200 dark:border-zinc-800 pt-4'>
                            Sem opções disponíveis para você no momento.
                        </p>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
}
