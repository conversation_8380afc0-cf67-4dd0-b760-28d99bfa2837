"use client";

import React, { useEffect, useState, useMemo, use<PERSON>allback } from "react";
import Image from "next/image";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { extractGoogleDriveId } from "@/app/components/deliveries-content/render-delivery-item";
import { <PERSON>, CardFooter, CardHeader } from "@/app/components/ui/card";
import Loading from "../ui/loading";
import { Button } from "@/app/components/ui/button";
import EditCaption from "@/app/components/edit-caption";
import ApproveDemandButton from "../approve-demand-button";
import { AddContentReview } from "../add-content-review";
import { RectangleVertical, Square, Download, MessageSquare, Trash, ChevronLeft, ChevronRight, PartyPopper } from "lucide-react";
import DeliveryDownloadModal from '@/app/components/delivery-download-modal';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogDescription,
} from '../ui/dialog';
import { Textarea } from '../ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { toast } from "sonner";

interface Content {
    details?: string;
    id: string;
    activityDate: string;
    status?: string;
    contentType?: string;
    destination?: string;
    channel?: string;
    canonicalChannel?: string;
    caption?: string;
    urlStructuringFeed?: string | string[];
    urlThumbnails?: string[];
    urlMediaTypes?: string[];
    urlTypes?: string[];
    urlStructuringFeedReviews?: string | string[];
    review?: string;
    reviewedBy?: { id?: string; name?: string; email?: string; image?: string };
    approved?: boolean;
}

type RawContent = { [key: string]: unknown };

interface WeeklyActivity {
    id?: string;
    description?: string;
    week?: number;
    contents?: RawContent[];
    approved?: boolean;
    status?: string;
}

interface MonthlyPlanning {
    id?: string;
    month?: number;
    year?: number;
    status?: string;
    activities?: WeeklyActivity[];
}

interface DeliveriesAllImagesProps {
    clientId: string;
}

const presignedCache = new Map<string, string>();

const encodeKeySegments = (key: string) => key.split('/').map(encodeURIComponent).join('/')

const getDisplaySrc = (source?: string) => {
    if (!source) return '/images/placeholder.png';
    try {
        if (source.startsWith('data:')) return source;
        if (/^(https?:)?\/\//i.test(source)) return source;

        const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL;
        if (publicBase) {
            return `${publicBase.replace(/\/$/, '')}/${encodeKeySegments(source)}`;
        }

        const bucket = process.env.NEXT_PUBLIC_S3_BUCKET;
        if (bucket) {
            return `https://${bucket}.s3.amazonaws.com/${encodeKeySegments(source)}`;
        }

        return source;
    } catch {
        return source;
    }
}

export const DeliveriesAllImages = ({ clientId }: DeliveriesAllImagesProps) => {
    const [contents, setContents] = useState<Content[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [localPresignedMap, setLocalPresignedMap] = useState<Record<string, string>>({});
    const [selectedChannel, setSelectedChannel] = useState<string>('Todos');
    const [downloadModalOpen, setDownloadModalOpen] = useState(false);
    const [downloadItems, setDownloadItems] = useState<Array<{ key: string; label?: string; thumb?: string | null }>>([]);

    const canonicalizeChannel = (raw?: string | null): string | null => {
        if (!raw) return null;
        const s = String(raw).trim().toLowerCase();
        if (!s) return null;
        if (s.includes('insta') || s.includes('instagram')) return 'Instagram';
        if (s.includes('facebook') || s.includes('fb')) return 'Facebook';
        if (s.includes('youtube') || s.includes('yt')) return 'YouTube';
        if (s.includes('tiktok')) return 'TikTok';
        if (s.includes('linkedin')) return 'LinkedIn';
        if (s.includes('twitter') || s.includes('x ')) return 'X';
        return 'Outros';
    };

    useEffect(() => {
        if (!clientId) return;

        const fetchData = async () => {
            try {
                setIsLoading(true);
                const res = await fetch(`/api/clients/${clientId}?include=monthlyPlannings.activities.contents`);
                if (!res.ok) throw new Error(`Erro ${res.status}`);
                const data = await res.json();

                const all: Content[] = [];

                if (Array.isArray(data.monthlyPlannings)) {
                    (data.monthlyPlannings as MonthlyPlanning[]).forEach((planning) => {
                        if (planning.status !== 'aprovado') return;

                        const approvedActivities = (planning.activities || []).filter(a => {
                            const wk = a as WeeklyActivity;
                            return wk.approved === true && wk.contents?.filter(c => c.status !== 'concluído').length === wk.contents?.length;
                        });

                        approvedActivities.forEach((activity) => {
                            (activity.contents || []).forEach((content) => {
                                const id = content?.['id'] ? String(content['id']) : undefined;
                                const activityDate = content?.['activityDate'] ? String(content['activityDate']) : undefined;
                                if (!id || !activityDate) return;

                                all.push({
                                    id,
                                    activityDate,
                                    details: typeof content['details'] === 'string' ? String(content['details']) : undefined,
                                    destination: typeof content['destination'] === 'string' ? String(content['destination']) : undefined,
                                    caption: typeof content['caption'] === 'string' ? String(content['caption']) : undefined,
                                    status: typeof content['status'] === 'string' ? String(content['status']) : undefined,
                                    review: typeof content['review'] === 'string' ? String(content['review']) : undefined,
                                    reviewedBy: typeof content['reviewedBy'] === 'object' ? (content['reviewedBy'] as unknown as Record<string, unknown>) : undefined,
                                    urlStructuringFeed: (Array.isArray(content['urlStructuringFeed']) || typeof content['urlStructuringFeed'] === 'string') ? (content['urlStructuringFeed'] as string | string[]) : undefined,
                                    // map per-URL review strings when present
                                    urlStructuringFeedReviews: Array.isArray(content['urlStructuringFeedReviews']) ? (content['urlStructuringFeedReviews'] as string[]) : (typeof content['urlStructuringFeedReviews'] === 'string' ? [String(content['urlStructuringFeedReviews'])] : undefined),
                                    urlThumbnails: Array.isArray(content['urlThumbnails']) ? (content['urlThumbnails'] as string[]) : undefined,
                                    urlMediaTypes: Array.isArray(content['urlMediaTypes']) ? (content['urlMediaTypes'] as string[]) : undefined,
                                    urlTypes: Array.isArray(content['urlTypes']) ? (content['urlTypes'] as string[]) : undefined,
                                    canonicalChannel: canonicalizeChannel(content['channel'] ? String(content['channel']) : (content['destination'] ? String(content['destination']) : null)) || undefined,
                                });
                            });
                        });
                    });
                }

                const sorted = all.sort((a, b) => new Date(a.activityDate).getTime() - new Date(b.activityDate).getTime());
                setContents(sorted);
            } catch (e) {
                console.error('Erro ao carregar imagens de entregas', e);
                setContents([]);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [clientId]);

    useEffect(() => {
        const keysToFetch: string[] = [];
        contents.forEach(c => {
            const urls = Array.isArray(c.urlStructuringFeed) ? c.urlStructuringFeed.filter(Boolean) as string[] : (c.urlStructuringFeed ? [String(c.urlStructuringFeed)] : []);
            (c.urlThumbnails || []).forEach(t => { if (t) urls.push(t); });

            urls.forEach(src => {
                if (!src) return;
                if (src.startsWith('data:') || /^(https?:)?\/\//i.test(src)) return;
                if (extractGoogleDriveId(src)) return;
                const key = src;
                if (!presignedCache.has(key) && !keysToFetch.includes(key)) keysToFetch.push(key);
                else if (presignedCache.has(key)) setLocalPresignedMap(prev => ({ ...prev, [key]: presignedCache.get(key)! }));
            });
        });

        if (keysToFetch.length === 0) return;

        keysToFetch.forEach(async key => {
            try {
                const res = await fetch('/api/uploads/presigned-get', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key })
                });
                if (!res.ok) return;
                const data = await res.json().catch(() => null);
                if (data?.url) {
                    presignedCache.set(key, String(data.url));
                    setLocalPresignedMap(prev => ({ ...prev, [key]: String(data.url) }));
                }
            } catch (e) {
                console.debug('presign failed', key, e);
            }
        });
    }, [contents]);

    const channelList = useMemo(() => {
        const set = new Set<string>();
        contents.forEach(c => {
            if (c.canonicalChannel) set.add(c.canonicalChannel);
        });

        const preferredOrder = ['Instagram', 'Facebook', 'YouTube', 'TikTok', 'LinkedIn', 'X', 'Outros'];
        const ordered: string[] = [];
        preferredOrder.forEach(p => { if (set.has(p)) ordered.push(p); });
        Array.from(set).forEach(s => { if (!ordered.includes(s)) ordered.push(s); });

        return ['Todos', ...ordered];
    }, [contents]);

    const channelLogos: Record<string, string | null> = {
        Instagram: '/Instagram_Glyph_Gradient.png',
        Facebook: '/Facebook_Logo_Primary.png',
        YouTube: '/yt_icon_red_digital.png',
        TikTok: null,
        Kaiwai: null,
        Behance: null,
        Pinterest: null,
        Outros: null,
    };

    const filteredContents = useMemo(() => {
        if (!selectedChannel || selectedChannel === 'Todos') return contents;
        return contents.filter(c => c.canonicalChannel === selectedChannel);
    }, [contents, selectedChannel]);

    const selectSrc = (source?: string) => {
        if (!source) return '/images/placeholder.png';
        if (source.startsWith('data:')) return source;
        const driveId = extractGoogleDriveId(source);
        if (driveId) return `/api/drive-proxy?id=${driveId}&quality=high&size=large`;
        if (/^(https?:)?\/\//i.test(source)) return source;
        const cached = localPresignedMap[source] || presignedCache.get(source);
        if (cached) return cached;

        // Avoid returning a raw relative key (e.g. "deliveries/uploads/..") which would
        // resolve to a same-origin path like /deliveries/uploads/... and cause 404.
        // Prefer the configured public S3 base or bucket if available; otherwise return placeholder
        // and wait for presigned-get to populate the URL and trigger a rerender.
        const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL;
        const bucket = process.env.NEXT_PUBLIC_S3_BUCKET;
        if (publicBase || bucket) {
            return getDisplaySrc(source);
        }

        return '/images/placeholder.png';
    };

    const isVideoUrl = (url?: string) => {
        if (!url) return false;
        const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
        return videoExtRegex.test(url) || videoExtRegex.test(selectSrc(url));
    };



    // Small internal helper: a horizontally scrollable row with edge gradients and keyboard nav
    const ScrollRow: React.FC<{ children: React.ReactNode; id?: string }> = ({ children, id }) => {
        const ref = React.useRef<HTMLDivElement | null>(null);

        React.useEffect(() => {
            const el = ref.current;
            if (!el) return;
            const onKey = (e: KeyboardEvent) => {
                if (document.activeElement && (document.activeElement as HTMLElement).tagName.toLowerCase() === 'textarea') return;
                if (e.key === 'ArrowRight') {
                    el.scrollBy({ left: 260, behavior: 'smooth' });
                } else if (e.key === 'ArrowLeft') {
                    el.scrollBy({ left: -260, behavior: 'smooth' });
                }
            };
            window.addEventListener('keydown', onKey);
            return () => window.removeEventListener('keydown', onKey);
        }, []);

        return (
            <div className="relative">
                <div className="absolute left-0 top-0 bottom-0 w-8 pointer-events-none bg-gradient-to-r from-white/90 dark:from-zinc-900/90" />
                <div className="absolute right-0 top-0 bottom-0 w-8 pointer-events-none bg-gradient-to-l from-white/90 dark:from-zinc-900/90" />
                <div ref={ref} id={id} className="flex gap-2 overflow-x-auto w-full flex-nowrap -mx-2 px-2">
                    {children}
                </div>
            </div>
        );
    };

    const handleReviewUpdate = (id: string, newReview: string) => {
        setContents(prev => prev.map(c => c.id === id ? { ...c, review: newReview } : c));
    };

    const handleStatusUpdate = (id: string, newStatus: string) => {
        setContents(prev => prev.map(c => c.id === id ? { ...c, status: newStatus } : c));
    };

    // --- Annotation (comentários por ponto) ---
    const [annotations, setAnnotations] = useState<Record<string, Array<{
        id: string;
        url: string;
        x: number;
        y: number;
        text: string;
        author?: { id: string; name?: string; email: string; image?: string };
        createdAt?: string;
        updatedAt?: string;
    }>>>({});

    // --- Modal Unificado (Solicitar ajuste + Comentar) ---
    const [unifiedModalOpen, setUnifiedModalOpen] = useState(false);
    const [unifiedModalContentId, setUnifiedModalContentId] = useState<string | null>(null);
    const [unifiedModalUrl, setUnifiedModalUrl] = useState<string | null>(null);
    const [unifiedModalMode, setUnifiedModalMode] = useState<'review' | 'comment'>('review');
    const [unifiedModalText, setUnifiedModalText] = useState<string>('');
    const [unifiedModalCoords, setUnifiedModalCoords] = useState<{ x: number; y: number } | null>(null);
    const [unifiedModalSaving, setUnifiedModalSaving] = useState(false);

    const unifiedModalIsVideo = isVideoUrl(unifiedModalUrl || undefined);

    useEffect(() => {
        // If the current unified modal target is a video, ensure mode is not 'comment'
        if (unifiedModalIsVideo && unifiedModalMode === 'comment') {
            setUnifiedModalMode('review');
        }
    }, [unifiedModalIsVideo, unifiedModalMode]);

    // Fullscreen gallery viewer
    const [fullscreenOpen, setFullscreenOpen] = useState(false);
    const [fullscreenGallery, setFullscreenGallery] = useState<string[]>([]);
    const [fullscreenIndex, setFullscreenIndex] = useState(0);
    const [fullscreenMediaTypes, setFullscreenMediaTypes] = useState<string[]>([]);
    const [fullscreenVideoUrls, setFullscreenVideoUrls] = useState<string[]>([]);

    const closeFullscreen = () => {
        setFullscreenOpen(false);
        setFullscreenGallery([]);
        setFullscreenIndex(0);
        setFullscreenMediaTypes([]);
        setFullscreenVideoUrls([]);
    }

    const nextFullscreen = useCallback(() => {
        setFullscreenIndex(i => (fullscreenGallery.length ? (i + 1) % fullscreenGallery.length : 0));
    }, [fullscreenGallery.length]);

    const prevFullscreen = useCallback(() => {
        setFullscreenIndex(i => (fullscreenGallery.length ? (i - 1 + fullscreenGallery.length) % fullscreenGallery.length : 0));
    }, [fullscreenGallery.length]);

    useEffect(() => {
        if (!fullscreenOpen) return;
        const handler = (e: KeyboardEvent) => {
            if (e.key === 'Escape') return closeFullscreen();
            if (e.key === 'ArrowRight') return nextFullscreen();
            if (e.key === 'ArrowLeft') return prevFullscreen();
        }
        window.addEventListener('keydown', handler);
        return () => window.removeEventListener('keydown', handler);
    }, [fullscreenOpen, nextFullscreen, prevFullscreen]);

    // --- Funções para carregar comentários da API ---
    const loadImageComments = async (contentId: string) => {
        try {
            const response = await fetch(`/api/contents/${contentId}/image-comments`);
            if (!response.ok) {
                console.error('Erro ao carregar comentários:', response.status);
                return;
            }
            const comments = await response.json();
            setAnnotations(prev => ({
                ...prev,
                [contentId]: comments
            }));
        } catch (error) {
            console.error('Erro ao carregar comentários:', error);
        }
    };

    // Carregar comentários quando os conteúdos mudarem
    useEffect(() => {
        contents.forEach(content => {
            loadImageComments(content.id);
        });
    }, [contents]);

    // --- Funções do Modal Unificado ---
    const openUnifiedModal = (contentId: string, url: string, mode: 'review' | 'comment' = 'review') => {
        setUnifiedModalContentId(contentId);
        setUnifiedModalUrl(url);
        setUnifiedModalMode(mode);
        setUnifiedModalCoords(null);
        setUnifiedModalText('');

        // Se for modo review, tentar pré-preencher com review existente
        if (mode === 'review') {
            const content = contents.find(c => c.id === contentId);
            if (content) {
                const arrUrls = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [String(content.urlStructuringFeed)] : []);
                const arrReviews = Array.isArray(content.urlStructuringFeedReviews) ? content.urlStructuringFeedReviews : (content.urlStructuringFeedReviews ? [String(content.urlStructuringFeedReviews)] : []);
                const idx = arrUrls.findIndex(u => String(u) === String(url));
                if (idx >= 0 && arrReviews[idx]) {
                    setUnifiedModalText(String(arrReviews[idx]));
                }
            }
        }

        setUnifiedModalOpen(true);
    };

    const openFullscreenForContent = (contentId: string, startingUrl?: string) => {
        const content = contents.find(c => c.id === contentId);
        if (!content) return;
        const urls = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed.filter(Boolean) as string[] : (content.urlStructuringFeed ? [String(content.urlStructuringFeed)] : []);
        // include thumbnails if any
        const thumbs = Array.isArray(content.urlThumbnails) ? content.urlThumbnails.filter(Boolean) as string[] : (content.urlThumbnails ? [String(content.urlThumbnails)] : []);

        // Build gallery items with display sources (thumbnails for videos, actual images for photos)
        const galleryItems = urls.map(u => {
            if (isVideoUrl(u)) {
                // For videos, try to use thumbnail if available, otherwise use video URL
                const urlIndex = urls.indexOf(u);
                const thumbnail = thumbs[urlIndex];
                return thumbnail ? selectSrc(thumbnail) : selectSrc(u);
            }
            return selectSrc(u);
        }).concat(thumbs.map(t => selectSrc(t))).filter(Boolean) as string[];

        // Build video URLs array (actual video URLs for videos, empty strings for images)
        const videoUrls = urls.map(u => isVideoUrl(u) ? selectSrc(u) : '').concat(thumbs.map(() => '')).filter((_, i) => galleryItems[i]);

        // Build media types array
        const mediaTypes = urls.map(u => isVideoUrl(u) ? 'video' : 'foto').concat(thumbs.map(() => 'foto')).filter((_, i) => galleryItems[i]);

        if (!galleryItems.length) return;
        const idx = startingUrl ? galleryItems.findIndex(s => s === selectSrc(startingUrl)) : 0;
        setFullscreenGallery(galleryItems);
        setFullscreenVideoUrls(videoUrls);
        setFullscreenMediaTypes(mediaTypes);
        setFullscreenIndex(idx >= 0 ? idx : 0);
        setFullscreenOpen(true);
    }

    const closeUnifiedModal = () => {
        setUnifiedModalOpen(false);
        setUnifiedModalContentId(null);
        setUnifiedModalUrl(null);
        setUnifiedModalMode('review');
        setUnifiedModalText('');
        setUnifiedModalCoords(null);
        setUnifiedModalSaving(false);
    };

    const onUnifiedModalImageClick = (e: React.MouseEvent<HTMLElement>) => {
        if (unifiedModalMode !== 'comment') return;
        if (!unifiedModalUrl || !unifiedModalContentId) return;

        const el = e.currentTarget as HTMLElement;
        const rect = el.getBoundingClientRect();
        const clientX = (e as React.MouseEvent).clientX;
        const clientY = (e as React.MouseEvent).clientY;
        const x = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100));
        const y = Math.max(0, Math.min(100, ((clientY - rect.top) / rect.height) * 100));
        setUnifiedModalCoords({ x, y });
    };

    const saveUnifiedModal = async () => {
        if (!unifiedModalContentId || !unifiedModalUrl) return;

        try {
            setUnifiedModalSaving(true);

            if (unifiedModalMode === 'review') {
                // Salvar review usando a mesma lógica do saveReviewDialog
                const contentId = unifiedModalContentId;
                const url = unifiedModalUrl;
                const text = unifiedModalText;

                const resp = await fetch(`/api/contents/${contentId}`);
                if (!resp.ok) throw new Error('Não foi possível obter o conteúdo');
                const contentData = await resp.json();

                const existingUrls: string[] = Array.isArray(contentData.urlStructuringFeed) ? contentData.urlStructuringFeed : (contentData.urlStructuringFeed ? [String(contentData.urlStructuringFeed)] : []);
                const existingReviews: string[] = Array.isArray(contentData.urlStructuringFeedReviews) ? contentData.urlStructuringFeedReviews : (contentData.urlStructuringFeedReviews ? [String(contentData.urlStructuringFeedReviews)] : []);

                type UrlItem = { url: string; review?: string };
                const merged: UrlItem[] = existingUrls.map((u: string, i: number) => ({ url: u, review: existingReviews[i] || '' }));
                const idx = merged.findIndex(m => String(m.url) === String(url));
                if (idx >= 0) merged[idx].review = text;
                else merged.push({ url, review: text });

                const existingTypes: string[] = Array.isArray(contentData.urlTypes) ? contentData.urlTypes : (contentData.urlTypes ? [String(contentData.urlTypes)] : []);
                const existingMediaTypes: string[] = Array.isArray(contentData.urlMediaTypes) ? contentData.urlMediaTypes : (contentData.urlMediaTypes ? [String(contentData.urlMediaTypes)] : []);
                const existingThumbnails: string[] = Array.isArray(contentData.urlThumbnails) ? contentData.urlThumbnails : (contentData.urlThumbnails ? [String(contentData.urlThumbnails)] : []);

                const urlsWithTypes = merged.map((item, i) => ({
                    url: item.url,
                    type: existingTypes[i] || 'feed',
                    mediaType: existingMediaTypes[i] || 'foto',
                    thumbnailUrl: existingThumbnails[i] || ''
                }));

                const patchResp = await fetch(`/api/contents/${contentId}/url-structuring`, {
                    method: 'PATCH',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ urlStructuringFeed: merged, urlsWithTypes })
                });

                if (!patchResp.ok) {
                    const txt = await patchResp.text().catch(() => '');
                    throw new Error(txt || `Erro ${patchResp.status}`);
                }

                // Atualizar estado local
                setContents(prev => prev.map(c => {
                    if (c.id !== contentId) return c;
                    const arrUrls = Array.isArray(c.urlStructuringFeed) ? c.urlStructuringFeed : (c.urlStructuringFeed ? [String(c.urlStructuringFeed)] : []);
                    const arrReviews = Array.isArray(c['urlStructuringFeedReviews']) ? c['urlStructuringFeedReviews'].slice() : (c['urlStructuringFeedReviews'] ? [String(c['urlStructuringFeedReviews'])] : []);
                    while (arrReviews.length < arrUrls.length) arrReviews.push('');
                    const idxLocal = arrUrls.findIndex(item => String(item) === String(url));
                    if (idxLocal >= 0) arrReviews[idxLocal] = text;
                    else arrReviews.push(text);
                    return { ...c, urlStructuringFeedReviews: arrReviews };
                }));

            } else if (unifiedModalMode === 'comment') {
                // Salvar comentário usando a API
                if (!unifiedModalCoords) return;

                const response = await fetch(`/api/contents/${unifiedModalContentId}/image-comments`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: unifiedModalUrl,
                        x: unifiedModalCoords.x,
                        y: unifiedModalCoords.y,
                        text: unifiedModalText || ''
                    }),
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `Erro ${response.status}`);
                }

                const newComment = await response.json();

                // Atualizar estado local
                setAnnotations(prev => {
                    const copy = { ...prev };
                    const key = unifiedModalContentId!;
                    copy[key] = copy[key] ? [...copy[key], newComment] : [newComment];
                    return copy;
                });
            }

            closeUnifiedModal();
        } catch (err) {
            console.error('Erro ao salvar no modal unificado:', err);
            if (unifiedModalMode === 'review') {
                toast.error('Erro ao salvar revisão. Tente novamente.');
            } else {
                toast.error('Erro ao salvar comentário. Tente novamente.');
            }
            closeUnifiedModal();
        }
    };

    const getAnnotationsFor = (contentId: string, url: string) => {
        const list = annotations[contentId] || [];
        return list.filter(a => String(a.url) === String(url));
    };

    const formatCommentDate = (dateString?: string) => {
        if (!dateString) return 'agora';
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now.getTime() - date.getTime();
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffMinutes < 1) return 'agora';
            if (diffMinutes < 60) return `${diffMinutes}min atrás`;
            if (diffHours < 24) return `${diffHours}h atrás`;
            if (diffDays < 7) return `${diffDays}d atrás`;
            return format(date, 'dd/MM/yyyy', { locale: ptBR });
        } catch {
            return 'agora';
        }
    };

    // popover for selected marker
    const [selectedAnnotation, setSelectedAnnotation] = useState<{ contentId: string; annotationId: string } | null>(null);
    const openAnnotationPopover = (contentId: string, annotationId: string) => {
        setSelectedAnnotation({ contentId, annotationId });
    };
    const closeAnnotationPopover = () => setSelectedAnnotation(null);

    const deleteAnnotation = async (contentId: string, annotationId: string) => {
        try {
            const response = await fetch(`/api/image-comments/${annotationId}`, {
                method: 'DELETE',
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || `Erro ${response.status}`);
            }

            setAnnotations(prev => {
                const copy = { ...prev };
                copy[contentId] = (copy[contentId] || []).filter(a => a.id !== annotationId);
                return copy;
            });
        } catch (error) {
            console.error('Erro ao deletar comentário:', error);
            toast.error('Erro ao deletar comentário. Tente novamente.');
        }
    };

    // close popover on outside click or Esc
    useEffect(() => {
        const onDoc = (e: MouseEvent) => {
            // if user clicked outside any open popover, close
            if (!selectedAnnotation) return;
            const target = e.target as HTMLElement | null;
            if (!target) return;
            // if click inside a popover element, do nothing (popovers have z-50)
            if (target.closest('.z-50')) return;
            closeAnnotationPopover();
        };

        const onKey = (e: KeyboardEvent) => {
            if (e.key === 'Escape') closeAnnotationPopover();
        };

        document.addEventListener('click', onDoc);
        document.addEventListener('keydown', onKey);
        return () => {
            document.removeEventListener('click', onDoc);
            document.removeEventListener('keydown', onKey);
        };
    }, [selectedAnnotation]);

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-96">
                <Loading />
            </div>
        );
    }

    if (contents.length === 0) {
        return (
            <div className="p-4 text-center text-sm text-muted-foreground">Nenhum conteúdo encontrado</div>
        );
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="mb-4 flex flex-wrap gap-2">
                {channelList.map(ch => (
                    <Button key={ch} variant={ch === selectedChannel ? 'secondary' : 'outline'} onClick={() => setSelectedChannel(ch)} className="flex items-center gap-2">
                        {channelLogos[ch] ? <Image src={channelLogos[ch] as string} alt={ch} width={16} height={16} /> : null}
                        <span>{ch}</span>
                    </Button>
                ))}
            </div>

            {filteredContents.map(content => {
                const formattedDate = format(new Date(content.activityDate), 'dd/MM/yyyy', { locale: ptBR });

                return (
                    <Card key={content.id} className="overflow-hidden">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <strong className="text-sm text-muted-foreground">{content.details || 'Conteúdo'}</strong>
                                </div>
                                <div className="flex flex-col items-end gap-2">
                                    <span className="text-sm">{formattedDate}</span>
                                    {content.status === 'aprovado' && (
                                        <Button size="sm" onClick={() => {
                                            const thumbs = Array.isArray(content.urlThumbnails) ? content.urlThumbnails : [];
                                            const urls = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [content.urlStructuringFeed] : []);
                                            const items = urls.map((u, i) => ({ key: String(u), label: undefined as string | undefined, thumb: (thumbs[i] ? selectSrc(thumbs[i]) : selectSrc(u)) }));
                                            setDownloadItems(items);
                                            setDownloadModalOpen(true);
                                        }} title="Visualizar/baixar arquivos">
                                            <Download size={14} />
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </CardHeader>

                        <div className="p-2 bg-gray-50 dark:bg-zinc-900">
                            <div className="flex flex-col w-full">
                                {(() => {
                                    // collect structuring urls and media type hints
                                    const structUrls = Array.isArray(content.urlStructuringFeed)
                                        ? content.urlStructuringFeed.filter(Boolean) as string[]
                                        : (content.urlStructuringFeed ? [String(content.urlStructuringFeed)] : []);
                                    const mediaTypes = Array.isArray(content.urlMediaTypes) ? content.urlMediaTypes.map(mt => String(mt).toLowerCase()) : [];
                                    const urlTypes = Array.isArray(content.urlTypes) ? content.urlTypes.map(ut => String(ut).toLowerCase()) : [];

                                    const feedUrls: string[] = [];
                                    const storyUrls: string[] = [];

                                    const regexIsStory = (u?: string) => {
                                        if (!u) return false;
                                        const s = String(u).toLowerCase();
                                        // common path segments and filename hints for stories/reels/igtv
                                        return /(^|\/)story(\/|$)|stories|\/stories\/|reel(s)?|igtv|\bstory[_-]?|type=story|format=story/gi.test(s);
                                    };

                                    structUrls.forEach((u, i) => {
                                        if (!u) return;
                                        const ut = urlTypes[i];
                                        const mt = mediaTypes[i];
                                        // Prefer explicit urlTypes provided by backend (feed/story)
                                        if (ut && ut.includes('story')) {
                                            storyUrls.push(u);
                                        } else if (mt && (mt.includes('story') || mt.includes('reel') || mt.includes('stories') || mt.includes('istant_story'))) {
                                            storyUrls.push(u);
                                        } else if (regexIsStory(u)) {
                                            storyUrls.push(u);
                                        } else {
                                            feedUrls.push(u);
                                        }
                                    });

                                    // resolve srcs
                                    const resolvedFeed = feedUrls.map(u => selectSrc(u)).filter(Boolean) as string[];
                                    const resolvedStory = storyUrls.map(u => selectSrc(u)).filter(Boolean) as string[];

                                    return (
                                        <>
                                            {/* Feed row: title + horizontal row */}
                                            {resolvedFeed.length > 0 && (
                                                <div className="w-full mb-3">
                                                    <div className="uppercase font-geistMono font-semibold p-4 flex gap-2 items-center">
                                                        <div className="bg-blue-200 dark:bg-blue-900 p-1 rounded-full">
                                                            <Square size={16} color="#3b82f6" />
                                                        </div>
                                                        <h4>
                                                            Feed
                                                        </h4>
                                                    </div>
                                                    <ScrollRow>
                                                        {resolvedFeed.map((u, i) => {
                                                            const src = selectSrc(u);
                                                            const poster = (() => {
                                                                if (Array.isArray(content.urlThumbnails) && content.urlThumbnails[0]) return selectSrc(content.urlThumbnails[0]);
                                                                return undefined;
                                                            })();

                                                            const isVideo = (() => {
                                                                // prefer explicit media type info on the content (if any entry contains video)
                                                                if (Array.isArray(content.urlMediaTypes) && content.urlMediaTypes.length > 0) {
                                                                    const anyVideo = content.urlMediaTypes.some(mt => typeof mt === 'string' && /video/i.test(mt));
                                                                    if (anyVideo) {
                                                                        // still confirm by extension on this specific url
                                                                        const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                                                                        if (videoExtRegex.test(u) || videoExtRegex.test(src)) return true;
                                                                    }
                                                                }

                                                                // fallback: check extension on resolved src
                                                                const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                                                                try {
                                                                    if (videoExtRegex.test(u)) return true;
                                                                    if (videoExtRegex.test(src)) return true;
                                                                } catch {
                                                                    // ignore
                                                                }

                                                                return false;
                                                            })();



                                                            // determine if this specific feed URL already has a review
                                                            const structUrlsAll = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [String(content.urlStructuringFeed)] : []);
                                                            const arrReviewsLocal = Array.isArray(content.urlStructuringFeedReviews) ? content.urlStructuringFeedReviews : (content.urlStructuringFeedReviews ? [String(content.urlStructuringFeedReviews)] : []);
                                                            const origIdx = structUrlsAll.findIndex(s => String(s) === String(feedUrls[i]));
                                                            const hasReview = origIdx >= 0 && typeof arrReviewsLocal[origIdx] === 'string' && arrReviewsLocal[origIdx].trim() !== '';

                                                            return (
                                                                <div
                                                                    key={`${content.id}-feed-${i}`}
                                                                    className={`flex-shrink-0 w-[260px] bg-gray-100 dark:bg-zinc-800 px-2 flex flex-col ${selectedAnnotation && (annotations[content.id] || []).some(a2 => a2.id === selectedAnnotation.annotationId && String(a2.url) === String(feedUrls[i])) ? 'relative z-[2000]' : ''}`}
                                                                >
                                                                    <div className="w-full flex justify-end gap-2 py-2">
                                                                        <div className="flex items-center gap-2">
                                                                            <Button
                                                                                size="sm"
                                                                                variant="outline"
                                                                                onClick={() => openUnifiedModal(content.id, feedUrls[i], 'review')}
                                                                                className={hasReview || getAnnotationsFor(content.id, feedUrls[i]).length > 0 ? 'border-amber-700 bg-amber-100 dark:bg-amber-950' : ''}
                                                                                disabled={content.status === 'aprovado'}
                                                                            >
                                                                                <MessageSquare size={14} className="mr-1" />
                                                                                Revisar arquivo
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex-1 relative flex items-center justify-center">
                                                                        {isVideo ? (
                                                                            <video controls className="max-h-full max-w-full rounded-sm" poster={poster}>
                                                                                <source src={src} />
                                                                                Seu navegador não suporta reprodução de vídeo.
                                                                            </video>
                                                                        ) : (
                                                                            <div className="relative w-full h-full">
                                                                                <div className="relative cursor-crosshair" onClick={() => {
                                                                                    // open unified modal in comment mode focused on this image
                                                                                    openUnifiedModal(content.id, feedUrls[i], 'comment');
                                                                                }}>
                                                                                    <Image src={src} alt={`Feed ${content.id} - ${i}`} width={1200} height={800} className="object-contain max-h-full max-w-full rounded-sm" unoptimized={true} />

                                                                                    {/* render markers */}
                                                                                    {getAnnotationsFor(content.id, feedUrls[i]).map(a => (
                                                                                        <div key={a.id} className="absolute" style={{ left: `${a.x}%`, top: `${a.y}%`, transform: 'translate(-50%,-50%)' }}>
                                                                                            <button onClick={(ev) => { ev.stopPropagation(); openAnnotationPopover(content.id, a.id); }} className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs" title="Abrir comentário">
                                                                                                !
                                                                                            </button>

                                                                                            {selectedAnnotation && selectedAnnotation.contentId === content.id && selectedAnnotation.annotationId === a.id ? (
                                                                                                <div
                                                                                                    className="absolute z-[9999] bg-white dark:bg-zinc-900 p-3 rounded shadow-xl border border-gray-200 dark:border-zinc-700 w-64"
                                                                                                    style={{
                                                                                                        left: a.x < 70 ? '100%' : 'auto',
                                                                                                        right: a.x >= 70 ? '100%' : 'auto',
                                                                                                        top: a.y > 80 ? 'auto' : '0',
                                                                                                        bottom: a.y > 80 ? '0' : 'auto',
                                                                                                        marginLeft: a.x < 70 ? '12px' : '0',
                                                                                                        marginRight: a.x >= 70 ? '12px' : '0'
                                                                                                    }}
                                                                                                >
                                                                                                    {/* Seta indicativa */}
                                                                                                    <div
                                                                                                        className="absolute w-3 h-3 bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-700 rotate-45"
                                                                                                        style={{
                                                                                                            left: a.x < 70 ? '-6px' : 'auto',
                                                                                                            right: a.x >= 70 ? '-6px' : 'auto',
                                                                                                            top: '20px',
                                                                                                            borderTopColor: 'transparent',
                                                                                                            borderRightColor: a.x < 70 ? 'transparent' : undefined,
                                                                                                            borderLeftColor: a.x >= 70 ? 'transparent' : undefined
                                                                                                        }}
                                                                                                    />
                                                                                                    <div className="text-sm mb-2 font-medium">{a.text}</div>
                                                                                                    <div className="text-xs text-muted-foreground mb-3">
                                                                                                        {a.author?.name || 'Usuário'} • {formatCommentDate(a.createdAt)}
                                                                                                    </div>
                                                                                                    <Button
                                                                                                        size="icon"
                                                                                                        variant="destructive"
                                                                                                        onClick={(ev) => { ev.stopPropagation(); deleteAnnotation(content.id, a.id); }}
                                                                                                    >
                                                                                                        <Trash size={14} />
                                                                                                    </Button>
                                                                                                </div>
                                                                                            ) : null}
                                                                                        </div>
                                                                                    ))}
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                    </ScrollRow>
                                                </div>
                                            )}

                                            {/* Story row below feed */}
                                            {resolvedStory.length > 0 && (
                                                <div className="w-full mt-2">
                                                    <div className="uppercase font-geistMono font-semibold p-4 flex gap-2 items-center">
                                                        <div className="bg-green-200 dark:bg-green-900 p-1 rounded-full">
                                                            <RectangleVertical size={16} color="#70a987" />
                                                        </div>
                                                        <h4>
                                                            Story
                                                        </h4>
                                                    </div>
                                                    <ScrollRow>
                                                        {resolvedStory.map((u, i) => {
                                                            const src = selectSrc(u);
                                                            const isVideo = (() => {
                                                                // prefer explicit media type info on the content (if any entry contains video)
                                                                if (Array.isArray(content.urlMediaTypes) && content.urlMediaTypes.length > 0) {
                                                                    const anyVideo = content.urlMediaTypes.some(mt => typeof mt === 'string' && /video/i.test(mt));
                                                                    if (anyVideo) {
                                                                        // still confirm by extension on this specific url
                                                                        const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                                                                        if (videoExtRegex.test(u) || videoExtRegex.test(src)) return true;
                                                                    }
                                                                }

                                                                // fallback: check extension on resolved src
                                                                const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                                                                try {
                                                                    if (videoExtRegex.test(u)) return true;
                                                                    if (videoExtRegex.test(src)) return true;
                                                                } catch {
                                                                    // ignore
                                                                }

                                                                return false;
                                                            })();



                                                            // determine if this specific story URL already has a review
                                                            const structUrlsAll = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [String(content.urlStructuringFeed)] : []);
                                                            const arrReviewsLocal = Array.isArray(content.urlStructuringFeedReviews) ? content.urlStructuringFeedReviews : (content.urlStructuringFeedReviews ? [String(content.urlStructuringFeedReviews)] : []);
                                                            const origIdx = structUrlsAll.findIndex(s => String(s) === String(storyUrls[i]));
                                                            const hasReview = origIdx >= 0 && typeof arrReviewsLocal[origIdx] === 'string' && arrReviewsLocal[origIdx].trim() !== '';

                                                            return (
                                                                <div
                                                                    key={`${content.id}-story-${i}`}
                                                                    className={`flex-shrink-0 w-[260px] bg-gray-100 dark:bg-zinc-800 px-2 flex flex-col ${selectedAnnotation && (annotations[content.id] || []).some(a2 => a2.id === selectedAnnotation.annotationId && String(a2.url) === String(storyUrls[i])) ? 'relative z-[2000]' : ''}`}
                                                                >
                                                                    <div className="w-full flex justify-end gap-2 py-2">
                                                                        <div className="flex items-center gap-2">
                                                                            <Button
                                                                                size="sm"
                                                                                variant="outline"
                                                                                className={hasReview ? 'border-amber-700 bg-amber-100 dark:bg-amber-950' : ''}
                                                                                onClick={() => openUnifiedModal(content.id, storyUrls[i], 'review')}
                                                                            >
                                                                                <MessageSquare size={14} />
                                                                                {hasReview ? 'Revisar arquivo' : 'Revisar arquivo'}
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                    <div className="flex-1 relative flex items-center justify-center">
                                                                        {isVideo ? (
                                                                            <video controls className="max-h-full max-w-full rounded-sm">
                                                                                <source src={src} />
                                                                                Seu navegador não suporta reprodução de vídeo.
                                                                            </video>
                                                                        ) : (
                                                                            <div className="relative w-full h-full">
                                                                                <div className="relative cursor-crosshair" onClick={() => {
                                                                                    // open unified modal in comment mode focused on this image
                                                                                    openUnifiedModal(content.id, storyUrls[i], 'comment');
                                                                                }}>
                                                                                    <Image src={src} alt={`Story ${content.id} - ${i}`} width={1200} height={800} className="object-contain max-h-full max-w-full rounded-sm" unoptimized={true} />

                                                                                    {getAnnotationsFor(content.id, storyUrls[i]).map(a => (
                                                                                        <div key={a.id} className="absolute" style={{ left: `${a.x}%`, top: `${a.y}%`, transform: 'translate(-50%,-50%)' }}>
                                                                                            <button onClick={(ev) => { ev.stopPropagation(); openAnnotationPopover(content.id, a.id); }} className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs" title="Abrir comentário">
                                                                                                !
                                                                                            </button>

                                                                                            {selectedAnnotation && selectedAnnotation.contentId === content.id && selectedAnnotation.annotationId === a.id ? (
                                                                                                <div
                                                                                                    className="absolute z-[9999] bg-white dark:bg-zinc-900 p-3 rounded shadow-xl border border-gray-200 dark:border-zinc-700 w-64"
                                                                                                    style={{
                                                                                                        left: a.x < 70 ? '100%' : 'auto',
                                                                                                        right: a.x >= 70 ? '100%' : 'auto',
                                                                                                        top: a.y > 80 ? 'auto' : '0',
                                                                                                        bottom: a.y > 80 ? '0' : 'auto',
                                                                                                        marginLeft: a.x < 70 ? '12px' : '0',
                                                                                                        marginRight: a.x >= 70 ? '12px' : '0'
                                                                                                    }}
                                                                                                >
                                                                                                    {/* Seta indicativa */}
                                                                                                    <div
                                                                                                        className="absolute w-3 h-3 bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-700 rotate-45"
                                                                                                        style={{
                                                                                                            left: a.x < 70 ? '-6px' : 'auto',
                                                                                                            right: a.x >= 70 ? '-6px' : 'auto',
                                                                                                            top: '20px',
                                                                                                            borderTopColor: 'transparent',
                                                                                                            borderRightColor: a.x < 70 ? 'transparent' : undefined,
                                                                                                            borderLeftColor: a.x >= 70 ? 'transparent' : undefined
                                                                                                        }}
                                                                                                    />
                                                                                                    <div className="text-sm mb-2 font-medium">{a.text}</div>
                                                                                                    <div className="text-xs text-muted-foreground mb-3">
                                                                                                        {a.author?.name || 'Usuário'} • {formatCommentDate(a.createdAt)}
                                                                                                    </div>
                                                                                                    <Button
                                                                                                        size="icon"
                                                                                                        variant="destructive"
                                                                                                        onClick={(ev) => { ev.stopPropagation(); deleteAnnotation(content.id, a.id); }}
                                                                                                    >
                                                                                                        <Trash size={14} />
                                                                                                    </Button>
                                                                                                </div>
                                                                                            ) : null}
                                                                                        </div>
                                                                                    ))}
                                                                                </div>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                    </ScrollRow>
                                                </div>
                                            )}

                                            {resolvedFeed.length === 0 && resolvedStory.length === 0 && (
                                                <div className="relative w-full h-96 bg-gray-100 dark:bg-zinc-800 flex items-center justify-center">
                                                    Nenhum arquivo encontrado
                                                </div>
                                            )}
                                        </>
                                    );
                                })()}
                            </div>

                            <div className="p-4 text-sm flex flex-col items-start gap-4">
                                <p className="whitespace-pre-line w-full md:w-1/2">
                                    {content.caption || 'Sem legenda'}
                                </p>

                                {content.caption && (
                                    <EditCaption contentId={content.id} initialCaption={content.caption} onSaved={(newCaption) => {
                                        setContents(prev => prev.map(p => p.id === content.id ? { ...p, caption: newCaption } : p));
                                    }} />
                                )}
                            </div>
                        </div>

                        <CardFooter className={`${content.status === 'aprovado' ? 'hidden' : 'flex'} flex-col sm:flex-row gap-2 mt-4 justify-center items-start`}>
                            <div className="w-full md:w-auto">
                                <AddContentReview
                                    contentId={content.id}
                                    label={content.review ? 'Solicitação de ajuste feita' : 'Solicitar ajuste geral'}
                                    disabled={content.status === 'aprovado' || content.status === 'concluído'}
                                    isClient
                                    currentReview={content.review || ''}
                                    reviewedBy={content.reviewedBy}
                                    onReviewUpdate={(newReview) => handleReviewUpdate(content.id, newReview)}
                                    type="content"
                                    onStatusUpdate={(newStatus) => handleStatusUpdate(content.id, newStatus)}
                                />
                                {content.review && (
                                    <p className="text-xs text-muted-foreground text-center mt-1">
                                        Solicitado por {content.reviewedBy?.name || 'Usuário anônimo'}
                                    </p>
                                )}
                            </div>

                            <div className="w-full md:w-auto">
                                <ApproveDemandButton
                                    contentId={content.id}
                                    disabled={Boolean(content.review)}
                                    currentStatus={content.status}
                                    onSuccess={() => {
                                        setContents(prev => prev.map(c => c.id === content.id ? { ...c, status: 'aprovado' } : c));
                                    }}
                                />
                            </div>
                        </CardFooter>

                        <div className={`${content.status === "aprovado" ? "flex" : "hidden"} justify-center my-4`}>
                            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-full">
                                <PartyPopper size={18} />
                                <span className="font-bold uppercase tracking-wider text-sm">{content.status}</span>
                            </div>
                        </div>
                    </Card>
                );
            })}

            <DeliveryDownloadModal open={downloadModalOpen} onOpenChange={setDownloadModalOpen} items={downloadItems} />

            <Dialog
                open={unifiedModalOpen}
                onOpenChange={(v) => { if (!v) closeUnifiedModal(); else setUnifiedModalOpen(true); }}
            >
                <DialogContent className="sm:max-w-[600px] max-h-[95vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Revisar arquivo</DialogTitle>
                        <DialogDescription>
                            Escolha como deseja revisar este arquivo: solicitar ajuste com texto ou adicionar marcador com comentário clicando na imagem.
                        </DialogDescription>
                    </DialogHeader>

                    <Tabs value={unifiedModalMode} onValueChange={(value) => {
                        // Prevent switching into comment mode when the target is a video
                        if (value === 'comment' && unifiedModalIsVideo) return;
                        setUnifiedModalMode(value as 'review' | 'comment')
                    }}>
                        <TabsList className="grid w-full grid-cols-2 overflow-x-auto">
                            <TabsTrigger value="review">Somente texto</TabsTrigger>
                            <TabsTrigger value="comment" disabled={unifiedModalIsVideo} aria-disabled={unifiedModalIsVideo}>Ponto específico</TabsTrigger>
                        </TabsList>

                        <TabsContent value="review" className="space-y-4">
                            <div className="w-full flex flex-col items-center justify-center mb-4">
                                <p className="text-sm text-muted-foreground mb-2">
                                    Clique ou toque para ampliar
                                </p>
                                {unifiedModalUrl ? (
                                    (() => {
                                        const previewSrc = selectSrc(unifiedModalUrl || undefined);
                                        const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                                        const isVideo = unifiedModalUrl ? (videoExtRegex.test(String(unifiedModalUrl)) || videoExtRegex.test(String(previewSrc))) : false;

                                        if (isVideo) {
                                            return (
                                                <video controls className="max-h-96 max-w-full" poster={previewSrc}>
                                                    <source src={previewSrc} />
                                                    Seu navegador não suporta reprodução de vídeo.
                                                </video>
                                            );
                                        }

                                        return (
                                            <div className="relative cursor-pointer" onClick={() => { if (unifiedModalMode === 'review' && unifiedModalContentId) openFullscreenForContent(unifiedModalContentId, unifiedModalUrl || undefined); }}>
                                                <Image src={previewSrc} alt="Arquivo para revisão" width={400} height={300} className="object-contain max-w-full rounded-sm" unoptimized={true} />
                                            </div>
                                        );
                                    })()
                                ) : null}
                            </div>
                            <Textarea
                                value={unifiedModalText}
                                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setUnifiedModalText(String(e.target.value))}
                                placeholder="Descreva o ajuste que você gostaria de solicitar para este arquivo..."
                                rows={3}
                            />
                        </TabsContent>

                        <TabsContent value="comment" className="space-y-4">
                            <div className="w-full flex items-center justify-center mb-4">
                                {unifiedModalUrl ? (
                                    (() => {
                                        const previewSrc = selectSrc(unifiedModalUrl || undefined);
                                        return (
                                            <div className="relative w-full overflow-hidden">
                                                <div className="relative cursor-crosshair" style={{ height: 500 }} onClick={onUnifiedModalImageClick}>
                                                    <Image src={previewSrc} alt="Imagem para comentar" fill className="object-contain" unoptimized={true} />
                                                    {unifiedModalCoords ? (
                                                        <div style={{ position: 'absolute', left: `${unifiedModalCoords.x}%`, top: `${unifiedModalCoords.y}%`, transform: 'translate(-50%,-50%)' }}>
                                                            <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs">!</div>
                                                        </div>
                                                    ) : null}
                                                </div>
                                            </div>
                                        );
                                    })()
                                ) : null}
                            </div>
                            <Textarea
                                placeholder="Clique na imagem acima para selecionar onde deseja adicionar o marcador e comentário, depois digite o texto aqui..."
                                value={unifiedModalText}
                                onChange={(e) => setUnifiedModalText(String(e.target.value))}
                                rows={3}
                            />
                        </TabsContent>
                    </Tabs>

                    <DialogFooter>
                        <Button variant="outline" onClick={closeUnifiedModal}>Cancelar</Button>
                        <Button
                            onClick={saveUnifiedModal}
                            disabled={unifiedModalSaving || (unifiedModalMode === 'comment' && !unifiedModalCoords)}
                        >
                            Salvar
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <Dialog open={fullscreenOpen} onOpenChange={(v) => { if (!v) closeFullscreen(); else setFullscreenOpen(true); }}>
                <DialogTitle></DialogTitle>
                <DialogContent className="max-w-7xl w-full max-h-[95vh] p-0 bg-black/90 border-zinc-500">
                    <div className="relative w-full h-[80vh] flex items-center justify-center">
                        {fullscreenGallery && fullscreenGallery.length ? (
                            <>
                                <div className="relative w-full h-[80vh]">
                                    {fullscreenMediaTypes[fullscreenIndex] === 'video' && fullscreenVideoUrls[fullscreenIndex] ? (
                                        <video
                                            src={fullscreenVideoUrls[fullscreenIndex]}
                                            controls
                                            autoPlay
                                            className="w-full h-full object-contain"
                                            style={{ maxWidth: '90vw', maxHeight: '80vh' }}
                                        >
                                            Seu navegador não suporta o elemento de vídeo.
                                        </video>
                                    ) : (
                                        <Image
                                            src={fullscreenGallery[fullscreenIndex]}
                                            alt={`${fullscreenMediaTypes[fullscreenIndex] === 'video' ? 'vídeo' : 'imagem'}-${fullscreenIndex}`}
                                            fill
                                            className="object-contain"
                                            unoptimized={true}
                                        />
                                    )}
                                </div>

                                <button className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 rounded-full p-2 z-40" onClick={prevFullscreen} aria-label="Anterior">
                                    <ChevronLeft className="text-white" />
                                </button>
                                <button className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 rounded-full p-2 z-40" onClick={nextFullscreen} aria-label="Próximo">
                                    <ChevronRight className="text-white" />
                                </button>

                                <div className="absolute left-1/2 -translate-x-1/2 bottom-2 text-white text-sm bg-black/40 px-3 py-1 rounded z-50">
                                    {fullscreenIndex + 1} / {fullscreenGallery.length}
                                    {fullscreenMediaTypes[fullscreenIndex] && (
                                        <span className="ml-2 text-xs opacity-75">
                                            ({fullscreenMediaTypes[fullscreenIndex] === 'video' ? 'Vídeo' : 'Foto'})
                                        </span>
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="text-white">Sem imagens</div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default DeliveriesAllImages;